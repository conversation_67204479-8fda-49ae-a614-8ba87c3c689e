---
// Продвинутый EditorJS компонент с поддержкой разных типов блоков
export interface Props {
  id?: string;
  placeholder?: string;
  data?: any;
  readonly?: boolean;
  minHeight?: number;
  onChange?: string;
  blockType?: string; // тип блока для определения набора инструментов
}

const {
  id = 'editorjs',
  placeholder = 'Начните писать...',
  data = null,
  readonly = false,
  minHeight = 300,
  onChange = null,
  blockType = 'text'
} = Astro.props;

const editorId = `editor-${id}`;
const hiddenInputId = `${id}-data`;
---

<div class="editorjs-wrapper">
  <div id={editorId} class="editorjs-container"></div>
  <input type="hidden" id={hiddenInputId} name={id} />
</div>

<script is:inline define:vars={{ editorId, hiddenInputId, placeholder, data, readonly, minHeight, onChange, blockType }}>
  window.editorjsConfig = window.editorjsConfig || {};
  window.editorjsConfig[editorId] = {
    editorId,
    hiddenInputId,
    placeholder,
    data,
    readonly,
    minHeight,
    onChange,
    blockType
  };
</script>

<script>
  import EditorJS from '@editorjs/editorjs';
  import Header from '@editorjs/header';
  import List from '@editorjs/list';
  import Paragraph from '@editorjs/paragraph';
  import Quote from '@editorjs/quote';
  import Code from '@editorjs/code';
  import Delimiter from '@editorjs/delimiter';
  import Table from '@editorjs/table';
  import Link from '@editorjs/link';
  import Image from '@editorjs/image';
  import Button from 'editorjs-button';
  import Embed from '@editorjs/embed';
  import Raw from '@editorjs/raw';

  // Динамически загружаем Grid Container
  let GridContainer = null;

  // Функция для динамической загрузки Grid Container
  async function loadGridContainer() {
    try {
      // Проверяем, загружен ли Grid Container из глобального контекста
      if (window.GridContainer) {
        GridContainer = window.GridContainer;
        console.log('✅ Grid Container загружен из window');
        return GridContainer;
      }

      // Если не загружен, загружаем динамически
      console.log('🔄 Загружаем Grid Container динамически...');

      // Загружаем скрипт динамически
      await new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = '/js/editorjs-tools/grid-container.js';
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });

      // Проверяем, загрузился ли Grid Container
      if (window.GridContainer) {
        GridContainer = window.GridContainer;
        console.log('✅ Grid Container загружен динамически');
        return GridContainer;
      } else {
        throw new Error('Grid Container не найден после загрузки');
      }
    } catch (error) {
      console.error('❌ Ошибка загрузки Grid Container:', error);
      return null;
    }
  }

  // Плагины успешно импортированы

  // Функция для получения инструментов для типа блока
  function getToolsForBlockType(blockType) {
    return toolConfigs[blockType] || toolConfigs.text;
  }

  // Переменная для отслеживания активного контейнера
  let activeContainerId = null;

  // Переменная для отслеживания количества блоков
  let lastBlockCount = 0;

  // Функция для обработки добавления блоков в контейнеры
  function setupBlockInterception(editor) {
    console.log('🔧 Настройка перехвата блоков, editor:', !!editor);

    if (!editor) {
      console.error('❌ Editor не доступен');
      return;
    }

    // Перехватываем API методы
    if (editor.blocks) {
      const originalInsert = editor.blocks.insert;
      editor.blocks.insert = function(type, data, config, index, needToFocus, replace) {
        console.log('🔍 API insert перехвачен:', { type, activeContainerId, data });

        if (activeContainerId && type !== 'gridContainer') {
          console.log('🎯 Перенаправляем в контейнер через API');

          // Находим активный контейнер и добавляем в него блок
          const activeContainerTool = window.GridContainerManager.getActiveContainer();
          if (activeContainerTool) {
            const blockData = {
              id: 'item_' + Math.random().toString(36).substr(2, 9),
              type: type,
              data: data || getDefaultDataForTool(type)
            };
            activeContainerTool.addItem(blockData);
          } else {
            console.warn('⚠️ Активный контейнер не найден в менеджере');
          }

          // Предотвращаем стандартное добавление блока
          return Promise.resolve();
        }

        // Вызываем оригинальный метод, если контейнер не активен
        return originalInsert.call(this, type, data, config, index, needToFocus, replace);
      };
    }

    // Перехватываем toolbar API
    if (editor.toolbar) {
      const originalOpen = editor.toolbar.open;
      editor.toolbar.open = function() {
        console.log('🔍 Toolbar open перехвачен, activeContainerId:', activeContainerId);
        return originalOpen.call(this);
      };
    }

    // Перехватываем клики по тулбару
    setTimeout(() => {
      const editorElement = document.querySelector('#' + editorId);
      if (editorElement) {
        console.log('🎯 Найден редактор, настраиваем глобальный перехват');

        // Перехватываем все клики в редакторе
        editorElement.addEventListener('click', (event) => {
          console.log('🔍 Клик в редакторе:', {
            target: event.target,
            activeContainerId,
            classList: event.target.classList?.toString()
          });

          const toolButton = event.target.closest('.ce-toolbox__button');
          if (toolButton) {
            console.log('🎯 Клик по кнопке тулбара:', {
              toolName: toolButton.dataset.tool,
              activeContainerId
            });

            if (activeContainerId && toolButton.dataset.tool !== 'gridContainer') {
              console.log('🚫 Перехватываем клик по инструменту');
              // Предотвращаем стандартное поведение
              event.preventDefault();
              event.stopPropagation();
              event.stopImmediatePropagation();

              handleToolClick(toolButton.dataset.tool);
              return false;
            }
          }

          // Также перехватываем клики по кнопке "+" (plus button)
          const plusButton = event.target.closest('.ce-toolbar__plus');
          if (plusButton && activeContainerId) {
            console.log('🎯 Клик по кнопке "+" при активном контейнере');
            // Не блокируем клик, но отслеживаем его
          }
        }, true); // Используем capture phase

        // Дополнительно перехватываем события на уровне документа
        document.addEventListener('click', (event) => {
          if (activeContainerId) {
            const toolButton = event.target.closest('.ce-toolbox__button');
            if (toolButton && toolButton.dataset.tool !== 'gridContainer') {
              const editorContainer = event.target.closest('#' + editorId);
              if (editorContainer) {
                console.log('🔍 Глобальный перехват клика по инструменту:', toolButton.dataset.tool);
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();

                handleToolClick(toolButton.dataset.tool);
                return false;
              }
            }
          }
        }, true);

      } else {
        console.warn('⚠️ Редактор не найден');
      }
    }, 1000);

    // Обработка клика по инструменту
    function handleToolClick(toolName, data) {
      console.log('🎯 Обработка клика по инструменту:', toolName);

      const activeContainer = document.querySelector(`[data-container-id="${activeContainerId}"]`);
      if (activeContainer) {
        const containerBlock = activeContainer.closest('.ce-block');
        const containerTool = containerBlock?.__editorjs_tool;

        if (containerTool && containerTool.addItem) {
          console.log('✅ Добавляем блок в контейнер:', toolName);

          const blockData = {
            id: 'item_' + Math.random().toString(36).substr(2, 9),
            type: toolName,
            data: data || getDefaultDataForTool(toolName)
          };

          containerTool.addItem(blockData);
          return true;
        } else {
          console.warn('⚠️ Не удалось найти containerTool или метод addItem');
        }
      } else {
        console.warn('⚠️ Не удалось найти активный контейнер');
      }
      return false;
    }

  // Функция для настройки наблюдателя за добавлением блоков
  function setupBlockObserver(editor) {
    console.log('🔧 Настройка наблюдателя за блоками');

    const editorElement = document.querySelector('#' + editorId);
    if (!editorElement) {
      console.warn('⚠️ Элемент редактора не найден для наблюдателя');
      return;
    }

    // Создаем MutationObserver для отслеживания добавления блоков
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE && node.classList?.contains('ce-block')) {
              console.log('🔍 Обнаружен новый блок через MutationObserver:', node);

              // Получаем тип блока
              const blockContent = node.querySelector('[data-tool]');
              if (blockContent) {
                const blockType = blockContent.getAttribute('data-tool');

                if (blockType && blockType !== 'gridContainer') {
                  console.log('📦 Новый блок типа:', blockType);

                  // Отправляем событие для Grid Container Manager
                  const blockData = {
                    id: 'item_' + Math.random().toString(36).substr(2, 9),
                    type: blockType,
                    data: getDefaultDataForTool(blockType)
                  };

                  const event = new CustomEvent('editorBlockAdded', {
                    detail: { blockData, blockIndex: -1 }
                  });
                  document.dispatchEvent(event);
                }
              }
            }
          });
        }
      });
    });

    // Начинаем наблюдение за редактором
    observer.observe(editorElement, {
      childList: true,
      subtree: true
    });

    console.log('✅ Наблюдатель за блоками настроен');
  }

    // Получение данных по умолчанию для инструмента
    function getDefaultDataForTool(toolName) {
      switch (toolName) {
        case 'paragraph':
          return { text: 'Новый параграф' };
        case 'header':
          return { text: 'Заголовок', level: 2 };
        case 'list':
          return { style: 'unordered', items: ['Элемент списка'] };
        case 'image':
          return { file: { url: '' }, caption: '', withBorder: false, withBackground: false, stretched: false };
        case 'quote':
          return { text: 'Цитата', caption: '', alignment: 'left' };
        case 'code':
          return { code: '// Ваш код здесь' };
        case 'delimiter':
          return {};
        case 'table':
          return { content: [['', '']], withHeadings: false };
        case 'linkTool':
          return { link: '', meta: {} };
        case 'embed':
          return { service: 'youtube', source: '', embed: '', width: 580, height: 320, caption: '' };
        case 'button':
          return { text: 'Кнопка', url: '#', style: 'primary' };
        default:
          return {};
      }
    }
  }

  // Слушаем события активации контейнеров
  document.addEventListener('gridContainerActivated', (event) => {
    activeContainerId = event.detail.containerId;
    console.log('🎯 Активирован контейнер:', activeContainerId);
  });

  document.addEventListener('gridContainerDeactivated', () => {
    activeContainerId = null;
    console.log('🎯 Контейнер деактивирован');
  });

  // ПРОСТОЙ ПОДХОД: Периодическая проверка и автоматическое перемещение блоков
  function setupSimpleBlockMigration(editor) {
    console.log('🔧 Настройка простой миграции блоков');

    let migrationActive = false;

    function migrateBlocks() {
      if (migrationActive || !activeContainerId) {
        return;
      }

      migrationActive = true;

      try {
        editor.save().then((outputData) => {
          if (!outputData || !outputData.blocks || outputData.blocks.length === 0) {
            migrationActive = false;
            return;
          }

          const activeContainer = window.GridContainerManager?.getActiveContainer();
          if (!activeContainer || !activeContainer.addItem) {
            migrationActive = false;
            return;
          }

          // Находим все блоки, кроме Grid Container
          const blocksToMove = [];
          outputData.blocks.forEach((block, index) => {
            if (block.type !== 'gridContainer') {
              blocksToMove.push({ block, index });
            }
          });

          if (blocksToMove.length === 0) {
            migrationActive = false;
            return;
          }

          console.log('🔄 Перемещаем блоки в активный контейнер:', blocksToMove.length);

          // Перемещаем блоки в контейнер
          blocksToMove.forEach(({ block }) => {
            const blockData = {
              id: 'item_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5),
              type: block.type,
              data: block.data || {}
            };

            activeContainer.addItem(blockData);
            console.log('✅ Блок перемещен:', block.type);
          });

          // Удаляем блоки из основного редактора (в обратном порядке)
          for (let i = blocksToMove.length - 1; i >= 0; i--) {
            const { index } = blocksToMove[i];
            try {
              editor.blocks.delete(index);
            } catch (error) {
              console.warn('⚠️ Ошибка удаления блока:', error);
            }
          }

          migrationActive = false;
        }).catch((error) => {
          console.error('❌ Ошибка миграции:', error);
          migrationActive = false;
        });
      } catch (error) {
        console.error('❌ Ошибка при проверке блоков:', error);
        migrationActive = false;
      }
    }

    // Запускаем проверку каждые 500ms когда есть активный контейнер
    setInterval(() => {
      if (activeContainerId && !migrationActive) {
        migrateBlocks();
      }
    }, 500);

    // Также запускаем миграцию при активации контейнера
    document.addEventListener('gridContainerActivated', () => {
      setTimeout(migrateBlocks, 300);
    });

    console.log('✅ Простая миграция блоков настроена');
  }

  // Функция для добавления Grid Container к инструментам
  async function addGridContainerToTools(tools) {
    console.log('🔍 Проверка Grid Container:', {
      localGridContainer: !!GridContainer,
      windowGridContainer: !!window.GridContainer,
      toolsKeys: Object.keys(tools)
    });

    // Пытаемся загрузить Grid Container если он не доступен
    if (!GridContainer) {
      GridContainer = await loadGridContainer();
    }

    const gridContainerClass = GridContainer || window.GridContainer;

    if (gridContainerClass) {
      console.log('✅ Grid Container найден, добавляем к инструментам');
      return {
        ...tools,
        gridContainer: {
          class: gridContainerClass,
          config: {
            placeholder: 'Grid Container'
          }
        }
      };
    } else {
      console.warn('⚠️ Grid Container не найден');
    }
    return tools;
  }

  // Конфигурации инструментов для разных типов блоков
  const toolConfigs = {
    text: {
      header: {
        class: Header,
        config: {
          placeholder: 'Введите заголовок...',
          levels: [1, 2, 3, 4, 5, 6],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      list: {
        class: List,
        inlineToolbar: true,
        config: {
          defaultStyle: 'unordered'
        }
      },
      quote: {
        class: Quote,
        inlineToolbar: true,
        config: {
          quotePlaceholder: 'Введите цитату',
          captionPlaceholder: 'Автор цитаты'
        }
      },
      code: {
        class: Code,
        config: {
          placeholder: 'Введите код...'
        }
      },
      delimiter: Delimiter,
      table: {
        class: Table,
        inlineToolbar: true,
        config: {
          rows: 2,
          cols: 3
        }
      },
      linkTool: {
        class: Link,
        config: {
          endpoint: '/api/settings/fetch-url'
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Текст кнопки',
          linkPlaceholder: 'Ссылка'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      }
    },
    hero: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок героя...',
          levels: [1, 2],
          defaultLevel: 1
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      image: {
        class: Image,
        config: {
          endpoints: {
            byFile: '/api/settings/editorjs-upload-image',
            byUrl: '/api/settings/editorjs-upload-image'
          }
        }
      },
      linkTool: {
        class: Link,
        config: {
          endpoint: '/api/settings/fetch-url'
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Текст кнопки',
          linkPlaceholder: 'Ссылка'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      },
      delimiter: Delimiter
    },
    features: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок особенности...',
          levels: [2, 3, 4],
          defaultLevel: 3
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      list: {
        class: List,
        inlineToolbar: true,
        config: {
          defaultStyle: 'unordered'
        }
      },
      image: {
        class: Image,
        config: {
          endpoints: {
            byFile: '/api/settings/editorjs-upload-image',
            byUrl: '/api/settings/editorjs-upload-image'
          }
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Текст кнопки',
          linkPlaceholder: 'Ссылка'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      }
    },
    gallery: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок галереи...',
          levels: [2, 3],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      image: {
        class: Image,
        config: {
          endpoints: {
            byFile: '/api/settings/editorjs-upload-image',
            byUrl: '/api/settings/editorjs-upload-image'
          }
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Текст кнопки',
          linkPlaceholder: 'Ссылка'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      },
      delimiter: Delimiter
    },

    // Контакты - для контактной информации
    contacts: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок контактов...',
          levels: [2, 3, 4],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      list: {
        class: List,
        inlineToolbar: true,
        config: {
          defaultStyle: 'unordered'
        }
      },
      linkTool: {
        class: Link,
        config: {
          endpoint: '/api/settings/fetch-url'
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Связаться с нами',
          linkPlaceholder: 'tel:+1234567890'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML для поля ввода (например: <input type="text" placeholder="Ваше имя" />)'
        }
      }
    },

    // Карта - для встраивания карт
    map: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок карты...',
          levels: [2, 3, 4],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true,
        config: {
          placeholder: 'Введите текст или вставьте ссылку на карту...'
        },
        toolbox: {
          icon: '<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg"><path d="M1.5 2.5h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2zm0 3h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2zm0 3h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2z"/></svg>',
          title: 'Параграф'
        }
      },
      embed: {
        class: Embed,
        inlineToolbar: false,
        shortcut: 'CMD+SHIFT+E',
        config: {
          services: {
            youtube: true,
            vimeo: true,
            googlemaps: true,
            yandexmaps: {
              regex: /https?:\/\/(www\.)?(yandex\.(ru|com)\/maps|maps\.yandex\.(ru|com))/,
              embedUrl: 'https://yandex.ru/map-widget/v1/?<%= remote_id %>',
              html: '<iframe src="https://yandex.ru/map-widget/v1/?<%= remote_id %>" width="100%" height="300" frameborder="0"></iframe>',
              height: 300,
              width: '100%',
              id: (groups) => {
                // Извлекаем параметры из URL Яндекс.Карт
                const url = groups.join('');
                const urlObj = new URL(url);
                return urlObj.search.substring(1); // убираем ? в начале
              }
            }
          }
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Построить маршрут',
          linkPlaceholder: 'https://maps.google.com'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      }
    },

    // Видео - для видеоконтента
    video: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок видео...',
          levels: [2, 3, 4],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true,
        config: {
          placeholder: 'Введите текст или вставьте ссылку для встраивания...'
        },
        toolbox: {
          icon: '<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg"><path d="M1.5 2.5h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2zm0 3h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2zm0 3h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2z"/></svg>',
          title: 'Параграф'
        }
      },
      embed: {
        class: Embed,
        inlineToolbar: false,
        shortcut: 'CMD+SHIFT+E',
        config: {
          services: {
            youtube: true,
            vimeo: true,
            coub: true,
            facebook: true,
            instagram: true,
            twitter: true,
            twitch: true
          }
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Смотреть больше',
          linkPlaceholder: 'https://youtube.com'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      },
      delimiter: Delimiter
    },

    // Формы - для форм обратной связи
    forms: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок формы...',
          levels: [2, 3, 4],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      list: {
        class: List,
        inlineToolbar: true,
        config: {
          defaultStyle: 'unordered'
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Отправить',
          linkPlaceholder: '#submit-form'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML для полей формы (например: <input type="email" placeholder="Email" required />)'
        }
      },
      delimiter: Delimiter
    },

    // Custom - универсальный тип со всеми инструментами
    custom: {
      header: {
        class: Header,
        config: {
          placeholder: 'Введите заголовок...',
          levels: [1, 2, 3, 4, 5, 6],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true,
        config: {
          placeholder: 'Введите текст...'
        }
      },
      list: {
        class: List,
        inlineToolbar: true,
        config: {
          defaultStyle: 'unordered'
        }
      },
      quote: {
        class: Quote,
        inlineToolbar: true,
        config: {
          quotePlaceholder: 'Введите цитату',
          captionPlaceholder: 'Автор цитаты'
        }
      },
      code: {
        class: Code,
        config: {
          placeholder: 'Введите код...'
        }
      },
      table: {
        class: Table,
        inlineToolbar: true,
        config: {
          rows: 2,
          cols: 3
        }
      },
      image: {
        class: Image,
        config: {
          endpoints: {
            byFile: '/api/settings/editorjs-upload-image',
            byUrl: '/api/settings/editorjs-upload-image'
          }
        }
      },
      linkTool: {
        class: Link,
        config: {
          endpoint: '/api/settings/fetch-url'
        }
      },
      embed: {
        class: Embed,
        inlineToolbar: false,
        shortcut: 'CMD+SHIFT+E',
        config: {
          services: {
            youtube: true,
            vimeo: true,
            coub: true,
            facebook: true,
            instagram: true,
            twitter: true,
            twitch: true,
            googlemaps: true,
            yandexmaps: {
              regex: /https?:\/\/(www\.)?(yandex\.(ru|com)\/map-widget|maps\.yandex\.(ru|com))/,
              embedUrl: '<%= remote_id %>',
              html: '<iframe src="<%= remote_id %>" width="100%" height="300" frameborder="0"></iframe>',
              height: 300,
              width: '100%'
            }
          }
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Текст кнопки',
          linkPlaceholder: 'Ссылка'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      },
      delimiter: Delimiter
    }
  };

  function initEditor() {
    const configs = window.editorjsConfig;
    if (!configs) {
      setTimeout(initEditor, 10);
      return;
    }

    Object.values(configs).forEach(config => {
      const {
        editorId,
        hiddenInputId,
        placeholder,
        data,
        readonly,
        minHeight,
        onChange,
        blockType
      } = config;

      async function createEditor() {
        try {
          const container = document.getElementById(editorId);
          if (!container) {
            return;
          }

          // Проверяем доступность Grid Container
          console.log('🔍 Проверка Grid Container при создании редактора:', {
            windowGridContainer: !!window.GridContainer,
            windowGridContainerManager: !!window.GridContainerManager
          });

          // Получаем базовые инструменты для типа блока
          let tools = getToolsForBlockType(blockType);

          console.log('🔧 Инструменты до добавления Grid Container:', Object.keys(tools));

          // Добавляем Grid Container если доступен (асинхронно)
          tools = await addGridContainerToTools(tools);

          console.log('🔧 Финальные инструменты:', Object.keys(tools));

          console.log('🚀 Создаем EditorJS с конфигурацией:', {
            holder: editorId,
            toolsCount: Object.keys(tools).length,
            hasData: !!(data && Object.keys(data).length),
            readonly,
            placeholder
          });

          let editor;

          editor = new EditorJS({
            holder: editorId,
            tools: tools,
            data: data || {},
            readOnly: readonly,
            placeholder: placeholder,
            minHeight: minHeight,
            onChange: async (api, event) => {
              try {
                const outputData = await editor.save();
                const hiddenInput = document.getElementById(hiddenInputId);

                if (hiddenInput) {
                  hiddenInput.value = JSON.stringify(outputData);
                }

                window.editorjsLastData = JSON.stringify(outputData);

                // Проверяем, был ли добавлен новый блок
                if (event && event.type === 'block-added') {
                  console.log('🔍 Обнаружено добавление блока через onChange:', event);

                  // Получаем информацию о добавленном блоке
                  const blockIndex = event.detail?.index;
                  if (blockIndex !== undefined) {
                    const blocks = outputData.blocks || [];
                    const addedBlock = blocks[blockIndex];

                    if (addedBlock) {
                      console.log('📦 Отправляем событие editorBlockAdded для блока:', addedBlock);

                      const blockData = {
                        id: addedBlock.id || 'item_' + Math.random().toString(36).substr(2, 9),
                        type: addedBlock.type,
                        data: addedBlock.data
                      };

                      const customEvent = new CustomEvent('editorBlockAdded', {
                        detail: { blockData, blockIndex }
                      });
                      document.dispatchEvent(customEvent);
                    }
                  }
                }

                if (onChange && typeof window[onChange] === 'function') {
                  window[onChange](outputData, api, event);
                }

              } catch (error) {
                // Ошибка при сохранении данных
              }
            },
            onReady: () => {
              // Устанавливаем минимальную высоту
              const editorContainer = document.querySelector('#' + editorId + ' .codex-editor');
              if (editorContainer) {
                editorContainer.style.minHeight = minHeight + 'px';
              }

              // Настраиваем перехват блоков для контейнеров
              setupBlockInterception(editor);
              console.log('🔧 Настроен перехват блоков для Grid Container');

              // Настраиваем отслеживание добавления блоков через MutationObserver
              // setupBlockObserver(editor); // Отключаем, так как перехватчик API справляется

              // Диагностика тулбокса
              setTimeout(() => {
                const toolbox = document.querySelector('#' + editorId + ' .ce-toolbox');
                if (toolbox) {
                  const toolButtons = toolbox.querySelectorAll('.ce-toolbox__button');
                  // Инструменты загружены
                }
              }, 1000);
            }
          });

          // Сохраняем экземпляр
          window[editorId + '_instance'] = editor;

          // Регистрируем редактор в менеджере контейнеров
          if (window.GridContainerManager) {
            window.GridContainerManager.registerEditor(editor);
          }

          // НОВЫЙ ПРОСТОЙ ПОДХОД: Периодическая проверка и перемещение блоков
          setupSimpleBlockMigration(editor);

          // Добавляем слушатель изменений grid-системы для обновления инструментов
          const updateToolsOnGridChange = () => {
            // Эта функция будет вызываться при изменении настроек grid
            console.log('Grid настройки изменились, обновляем инструменты...');
          };

          // Слушаем изменения в grid-системе
          document.addEventListener('change', (e) => {
            if (e.target.name === 'gridEnabled' || e.target.name?.startsWith('gridMode')) {
              updateToolsOnGridChange();
            }
          });

          // Добавляем флаг готовности
          window[editorId + '_ready'] = true;

          // Функция для обновления инструментов
          window.updateEditorTools = function() {
            const blockTypeSelect = document.getElementById('block-type');
            if (blockTypeSelect) {
              const newBlockType = blockTypeSelect.value;

              // Получаем базовые инструменты для нового типа
              let newTools = getToolsForBlockType(newBlockType);

              // Добавляем Grid Container если доступен
              newTools = addGridContainerToTools(newTools);
              const toolsInfo = Object.keys(newTools);

              // Показываем уведомление пользователю
              showToolsNotification(newBlockType, toolsInfo);

              // Пересоздаем редактор с новыми инструментами
              recreateEditor(newBlockType, newTools);
            }
          };

          // Функция для показа уведомления
          function showToolsNotification(blockType, tools) {
            // Удаляем предыдущее уведомление если есть
            const existingNotification = document.querySelector('.tools-notification');
            if (existingNotification) {
              existingNotification.remove();
            }

            const notification = document.createElement('div');
            notification.className = 'tools-notification fixed top-4 right-4 bg-blue-500 text-white px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm';

            const titleDiv = document.createElement('div');
            titleDiv.className = 'font-semibold mb-1';
            titleDiv.textContent = 'Тип блока: ' + blockType;

            const labelDiv = document.createElement('div');
            labelDiv.className = 'text-sm';
            labelDiv.textContent = 'Доступные инструменты:';

            const toolsDiv = document.createElement('div');
            toolsDiv.className = 'text-sm mt-1';
            toolsDiv.textContent = tools.join(', ');

            notification.appendChild(titleDiv);
            notification.appendChild(labelDiv);
            notification.appendChild(toolsDiv);

            document.body.appendChild(notification);

            setTimeout(() => {
              notification.remove();
            }, 5000);
          }

          // Функция для пересоздания редактора
          function recreateEditor(blockType, tools) {
            try {
              // Сбрасываем флаг готовности
              window[editorId + '_ready'] = false;

              // Уничтожаем старый редактор
              const oldEditor = window[editorId + '_instance'];
              if (oldEditor && typeof oldEditor.destroy === 'function') {
                oldEditor.destroy();
              }

              // Очищаем контейнер
              const container = document.getElementById(editorId);
              if (container) {
                container.innerHTML = '';
              }

              // Создаем новый редактор с новыми инструментами
              setTimeout(() => {
                const newEditor = new EditorJS({
                  holder: editorId,
                  tools: tools,
                  data: {},
                  readOnly: readonly,
                  placeholder: placeholder,
                  minHeight: minHeight,
                  onChange: async (api, event) => {
                    try {
                      const outputData = await newEditor.save();
                      const hiddenInput = document.getElementById(hiddenInputId);

                      if (hiddenInput) {
                        hiddenInput.value = JSON.stringify(outputData);
                      }

                      window.editorjsLastData = JSON.stringify(outputData);

                      if (onChange && typeof window[onChange] === 'function') {
                        window[onChange](outputData, api, event);
                      }

                    } catch (error) {
                      // Ошибка при сохранении данных
                    }
                  },
                  onReady: () => {

                    const editorContainer = document.querySelector('#' + editorId + ' .codex-editor');
                    if (editorContainer) {
                      editorContainer.style.minHeight = minHeight + 'px';
                    }
                  }
                });

                // Сохраняем новый экземпляр
                window[editorId + '_instance'] = newEditor;
                window[editorId + '_ready'] = true;
              }, 100);

            } catch (error) {
              // Ошибка при пересоздании редактора
            }
          }

        } catch (error) {
          showError(editorId);
        }
      }

      function showError(editorId) {
        const container = document.getElementById(editorId);
        if (container) {
          container.innerHTML =
            '<div style="padding: 20px; border: 2px dashed #ef4444; border-radius: 8px; text-align: center; color: #ef4444;">' +
              '<p><strong>Ошибка загрузки редактора</strong></p>' +
              '<p>Не удалось создать EditorJS. Проверьте консоль для деталей.</p>' +
              '<button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #ef4444; color: white; border: none; border-radius: 4px; cursor: pointer;">' +
                'Перезагрузить страницу' +
              '</button>' +
            '</div>';
        }
      }

      setTimeout(() => createEditor(), 100);
    });
  }

  // Инициализация редактора (Grid Container загружается динамически при необходимости)
  function initEditorWithGridCheck() {
    console.log('🚀 Инициализируем редактор с динамической загрузкой Grid Container');
    initEditor();
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initEditorWithGridCheck);
  } else {
    initEditorWithGridCheck();
  }
</script>

<style>
  .editorjs-wrapper {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background: white;
    overflow: hidden;
  }

  .editorjs-container {
    min-height: 300px;
    padding: 1rem;
  }

  /* Стили для EditorJS */
  :global(.codex-editor) {
    font-family: inherit;
  }

  :global(.codex-editor__redactor) {
    padding-bottom: 1rem !important;
  }

  :global(.ce-block__content) {
    max-width: none !important;
  }

  :global(.ce-toolbar__content) {
    max-width: none !important;
  }

  :global(.ce-block) {
    margin: 0.5rem 0;
  }

  :global(.ce-paragraph) {
    line-height: 1.6;
  }

  :global(.ce-header) {
    margin: 1rem 0 0.5rem 0;
  }

  :global(.ce-quote) {
    border-left: 4px solid #3b82f6;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
  }

  :global(.ce-code) {
    background: #f3f4f6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    margin: 1rem 0;
  }

  :global(.ce-delimiter) {
    margin: 2rem 0;
    text-align: center;
  }

  :global(.ce-table) {
    margin: 1rem 0;
  }

  :global(.ce-table table) {
    width: 100%;
    border-collapse: collapse;
  }

  :global(.ce-table td) {
    border: 1px solid #e5e7eb;
    padding: 0.5rem;
  }

  :global(.ce-inline-toolbar) {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  :global(.codex-editor__redactor) {
    padding-left: 6%;
  }

  :global(.ce-toolbar) {
    left: 6% !important;
  }
</style>
